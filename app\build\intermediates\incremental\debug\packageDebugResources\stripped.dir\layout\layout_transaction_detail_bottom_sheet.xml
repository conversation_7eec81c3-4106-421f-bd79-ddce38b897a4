<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_background"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 交易头部信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingBottom="16dp">

        <ImageView
            android:id="@+id/transaction_detail_type_icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:contentDescription="@string/receipt_desc_交易类型图标"
            tools:src="@drawable/ic_category_food" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/transaction_detail_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold"
                tools:text="¥128.50" />

            <TextView
                android:id="@+id/transaction_detail_type_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="@color/black"
                android:textSize="12sp"
                tools:text="支出" />

        </LinearLayout>

        <ImageView
            android:id="@+id/transaction_detail_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/receipt_关闭"
            android:src="@drawable/ic_close"
            app:tint="@color/black" />

    </LinearLayout>

    <!-- 详细信息区域 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 分类信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingBottom="12dp">

                <ImageView
                    android:id="@+id/transaction_detail_category_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:contentDescription="@string/receipt_desc_分类图标"
                    tools:src="@drawable/ic_category_food" />

                <TextView
                    android:id="@+id/transaction_detail_category_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    tools:text="餐饮" />

            </LinearLayout>

            <!-- 账户信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingBottom="12dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:contentDescription="@string/receipt_desc_账户图标"
                    android:src="@drawable/ic_account"
                    app:tint="@color/black" />

                <TextView
                    android:id="@+id/transaction_detail_account_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    tools:text="招商银行储蓄卡" />

            </LinearLayout>

            <!-- 时间信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingBottom="12dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:contentDescription="@string/receipt_desc_时间图标"
                    android:src="@drawable/ic_time"
                    app:tint="@color/black" />

                <TextView
                    android:id="@+id/transaction_detail_datetime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    tools:text="2025年1月15日 14:30" />

            </LinearLayout>

            <!-- 备注信息 -->
            <LinearLayout
                android:id="@+id/transaction_detail_remark_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="top"
                android:orientation="horizontal"
                android:paddingBottom="12dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:contentDescription="@string/receipt_desc_备注图标"
                    android:src="@drawable/ic_note"
                    app:tint="@color/black" />

                <TextView
                    android:id="@+id/transaction_detail_remark"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    tools:text="午餐费用" />

            </LinearLayout>

            <!-- 标签信息 -->
            <LinearLayout
                android:id="@+id/transaction_detail_tags_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="top"
                android:orientation="horizontal"
                android:visibility="gone">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:contentDescription="@string/receipt_desc_标签图标"
                    android:src="@drawable/ic_tag"
                    app:tint="@color/black" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/transaction_detail_tags_list"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 操作按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/transaction_detail_edit_button"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/widget_small_button_bg"
            android:backgroundTint="@color/HuaQing"
            android:gravity="center"
            android:text="@string/receipt_编辑"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/transaction_detail_delete_button"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:layout_weight="1"
            android:background="@drawable/widget_small_button_bg"
            android:backgroundTint="@color/ChaHuaHong"
            android:gravity="center"
            android:text="@string/receipt_删除"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/transaction_detail_refund_button"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:background="@drawable/widget_small_button_bg"
            android:backgroundTint="@color/black"
            android:gravity="center"
            android:text="@string/receipt_退款"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>
