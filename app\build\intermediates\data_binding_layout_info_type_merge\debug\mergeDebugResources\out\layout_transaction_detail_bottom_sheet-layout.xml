<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_transaction_detail_bottom_sheet" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\layout_transaction_detail_bottom_sheet.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/layout_transaction_detail_bottom_sheet_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="265" endOffset="14"/></Target><Target id="@+id/transaction_detail_type_icon" view="ImageView"><Expressions/><location startLine="18" startOffset="8" endLine="24" endOffset="52"/></Target><Target id="@+id/transaction_detail_amount" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="38"/></Target><Target id="@+id/transaction_detail_type_text" view="TextView"><Expressions/><location startLine="41" startOffset="12" endLine="48" endOffset="33"/></Target><Target id="@+id/transaction_detail_close" view="ImageView"><Expressions/><location startLine="52" startOffset="8" endLine="59" endOffset="37"/></Target><Target id="@+id/transaction_detail_category_icon" view="ImageView"><Expressions/><location startLine="85" startOffset="16" endLine="91" endOffset="60"/></Target><Target id="@+id/transaction_detail_category_name" view="TextView"><Expressions/><location startLine="93" startOffset="16" endLine="99" endOffset="37"/></Target><Target id="@+id/transaction_detail_account_name" view="TextView"><Expressions/><location startLine="119" startOffset="16" endLine="125" endOffset="42"/></Target><Target id="@+id/transaction_detail_datetime" view="TextView"><Expressions/><location startLine="145" startOffset="16" endLine="151" endOffset="51"/></Target><Target id="@+id/transaction_detail_remark_container" view="LinearLayout"><Expressions/><location startLine="156" startOffset="12" endLine="182" endOffset="26"/></Target><Target id="@+id/transaction_detail_remark" view="TextView"><Expressions/><location startLine="173" startOffset="16" endLine="180" endOffset="39"/></Target><Target id="@+id/transaction_detail_tags_container" view="LinearLayout"><Expressions/><location startLine="185" startOffset="12" endLine="208" endOffset="26"/></Target><Target id="@+id/transaction_detail_tags_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="201" startOffset="16" endLine="206" endOffset="60"/></Target><Target id="@+id/transaction_detail_edit_button" view="TextView"><Expressions/><location startLine="220" startOffset="8" endLine="232" endOffset="38"/></Target><Target id="@+id/transaction_detail_delete_button" view="TextView"><Expressions/><location startLine="234" startOffset="8" endLine="247" endOffset="38"/></Target><Target id="@+id/transaction_detail_refund_button" view="TextView"><Expressions/><location startLine="249" startOffset="8" endLine="261" endOffset="38"/></Target></Targets></Layout>