<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_receipt" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\fragment_receipt.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_receipt_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="128" endOffset="51"/></Target><Target id="@+id/receipt_Receipts" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="18" endOffset="51"/></Target><Target id="@+id/receipt_Setting" view="ImageView"><Expressions/><location startLine="20" startOffset="4" endLine="32" endOffset="35"/></Target><Target id="@+id/receipt_Year_Month_Container" view="LinearLayout"><Expressions/><location startLine="34" startOffset="4" endLine="66" endOffset="18"/></Target><Target id="@+id/receipt_Year_Month" view="TextView"><Expressions/><location startLine="48" startOffset="8" endLine="55" endOffset="38"/></Target><Target id="@+id/receipt_Year_Month_Arrow" view="ImageView"><Expressions/><location startLine="57" startOffset="8" endLine="64" endOffset="37"/></Target><Target id="@+id/receipt_InOut_Budget_Container" view="com.example.likeqianwang.Utils.NestedScrollableHost"><Expressions/><location startLine="68" startOffset="4" endLine="83" endOffset="57"/></Target><Target id="@+id/receipt_InOut_Budget_Widget" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="75" startOffset="8" endLine="81" endOffset="51"/></Target><Target id="@+id/receipt_InOut_Budget_WidgetTabs" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="85" startOffset="4" endLine="98" endOffset="36"/></Target><Target id="@+id/receipt_Daily_InOut_List" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="100" startOffset="4" endLine="109" endOffset="56"/></Target><Target id="@+id/button_Add" view="ImageButton"><Expressions/><location startLine="111" startOffset="4" endLine="126" endOffset="33"/></Target></Targets></Layout>