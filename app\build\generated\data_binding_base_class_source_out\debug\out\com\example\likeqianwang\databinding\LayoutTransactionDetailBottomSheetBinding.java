// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutTransactionDetailBottomSheetBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView transactionDetailAccountName;

  @NonNull
  public final TextView transactionDetailAmount;

  @NonNull
  public final ImageView transactionDetailCategoryIcon;

  @NonNull
  public final TextView transactionDetailCategoryName;

  @NonNull
  public final ImageView transactionDetailClose;

  @NonNull
  public final TextView transactionDetailDatetime;

  @NonNull
  public final TextView transactionDetailDeleteButton;

  @NonNull
  public final TextView transactionDetailEditButton;

  @NonNull
  public final TextView transactionDetailRefundButton;

  @NonNull
  public final TextView transactionDetailRemark;

  @NonNull
  public final LinearLayout transactionDetailRemarkContainer;

  @NonNull
  public final LinearLayout transactionDetailTagsContainer;

  @NonNull
  public final RecyclerView transactionDetailTagsList;

  @NonNull
  public final ImageView transactionDetailTypeIcon;

  @NonNull
  public final TextView transactionDetailTypeText;

  private LayoutTransactionDetailBottomSheetBinding(@NonNull LinearLayout rootView,
      @NonNull TextView transactionDetailAccountName, @NonNull TextView transactionDetailAmount,
      @NonNull ImageView transactionDetailCategoryIcon,
      @NonNull TextView transactionDetailCategoryName, @NonNull ImageView transactionDetailClose,
      @NonNull TextView transactionDetailDatetime, @NonNull TextView transactionDetailDeleteButton,
      @NonNull TextView transactionDetailEditButton,
      @NonNull TextView transactionDetailRefundButton, @NonNull TextView transactionDetailRemark,
      @NonNull LinearLayout transactionDetailRemarkContainer,
      @NonNull LinearLayout transactionDetailTagsContainer,
      @NonNull RecyclerView transactionDetailTagsList, @NonNull ImageView transactionDetailTypeIcon,
      @NonNull TextView transactionDetailTypeText) {
    this.rootView = rootView;
    this.transactionDetailAccountName = transactionDetailAccountName;
    this.transactionDetailAmount = transactionDetailAmount;
    this.transactionDetailCategoryIcon = transactionDetailCategoryIcon;
    this.transactionDetailCategoryName = transactionDetailCategoryName;
    this.transactionDetailClose = transactionDetailClose;
    this.transactionDetailDatetime = transactionDetailDatetime;
    this.transactionDetailDeleteButton = transactionDetailDeleteButton;
    this.transactionDetailEditButton = transactionDetailEditButton;
    this.transactionDetailRefundButton = transactionDetailRefundButton;
    this.transactionDetailRemark = transactionDetailRemark;
    this.transactionDetailRemarkContainer = transactionDetailRemarkContainer;
    this.transactionDetailTagsContainer = transactionDetailTagsContainer;
    this.transactionDetailTagsList = transactionDetailTagsList;
    this.transactionDetailTypeIcon = transactionDetailTypeIcon;
    this.transactionDetailTypeText = transactionDetailTypeText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutTransactionDetailBottomSheetBinding inflate(
      @NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutTransactionDetailBottomSheetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_transaction_detail_bottom_sheet, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutTransactionDetailBottomSheetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.transaction_detail_account_name;
      TextView transactionDetailAccountName = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailAccountName == null) {
        break missingId;
      }

      id = R.id.transaction_detail_amount;
      TextView transactionDetailAmount = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailAmount == null) {
        break missingId;
      }

      id = R.id.transaction_detail_category_icon;
      ImageView transactionDetailCategoryIcon = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailCategoryIcon == null) {
        break missingId;
      }

      id = R.id.transaction_detail_category_name;
      TextView transactionDetailCategoryName = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailCategoryName == null) {
        break missingId;
      }

      id = R.id.transaction_detail_close;
      ImageView transactionDetailClose = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailClose == null) {
        break missingId;
      }

      id = R.id.transaction_detail_datetime;
      TextView transactionDetailDatetime = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailDatetime == null) {
        break missingId;
      }

      id = R.id.transaction_detail_delete_button;
      TextView transactionDetailDeleteButton = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailDeleteButton == null) {
        break missingId;
      }

      id = R.id.transaction_detail_edit_button;
      TextView transactionDetailEditButton = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailEditButton == null) {
        break missingId;
      }

      id = R.id.transaction_detail_refund_button;
      TextView transactionDetailRefundButton = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailRefundButton == null) {
        break missingId;
      }

      id = R.id.transaction_detail_remark;
      TextView transactionDetailRemark = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailRemark == null) {
        break missingId;
      }

      id = R.id.transaction_detail_remark_container;
      LinearLayout transactionDetailRemarkContainer = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailRemarkContainer == null) {
        break missingId;
      }

      id = R.id.transaction_detail_tags_container;
      LinearLayout transactionDetailTagsContainer = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailTagsContainer == null) {
        break missingId;
      }

      id = R.id.transaction_detail_tags_list;
      RecyclerView transactionDetailTagsList = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailTagsList == null) {
        break missingId;
      }

      id = R.id.transaction_detail_type_icon;
      ImageView transactionDetailTypeIcon = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailTypeIcon == null) {
        break missingId;
      }

      id = R.id.transaction_detail_type_text;
      TextView transactionDetailTypeText = ViewBindings.findChildViewById(rootView, id);
      if (transactionDetailTypeText == null) {
        break missingId;
      }

      return new LayoutTransactionDetailBottomSheetBinding((LinearLayout) rootView,
          transactionDetailAccountName, transactionDetailAmount, transactionDetailCategoryIcon,
          transactionDetailCategoryName, transactionDetailClose, transactionDetailDatetime,
          transactionDetailDeleteButton, transactionDetailEditButton, transactionDetailRefundButton,
          transactionDetailRemark, transactionDetailRemarkContainer, transactionDetailTagsContainer,
          transactionDetailTagsList, transactionDetailTypeIcon, transactionDetailTypeText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
