package com.example.likeqianwang.ui.dialogs;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.SettingsMenuItem;
import com.example.likeqianwang.R;
import com.example.likeqianwang.adapters.SettingsMenuAdapter;
import com.example.likeqianwang.ui.budget_settings.BudgetSettingsActivity;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.util.ArrayList;
import java.util.List;

public class SettingsMenuBottomSheetDialogFragment extends BottomSheetDialogFragment 
        implements SettingsMenuAdapter.OnSettingsMenuItemClickListener {

    private static final String TAG = "SettingsMenu";

    // UI组件
    private ImageView closeButton;
    private RecyclerView menuRecyclerView;
    private SettingsMenuAdapter menuAdapter;

    // 数据
    private List<SettingsMenuItem> menuItems;

    public static SettingsMenuBottomSheetDialogFragment newInstance() {
        return new SettingsMenuBottomSheetDialogFragment();
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            setupFullHeight(bottomSheetDialog);
        });
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.layout_settings_menu_bottom_sheet, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupMenuItems();
        setupRecyclerView();
        setupClickListeners();
    }

    private void initViews(View view) {
        closeButton = view.findViewById(R.id.settings_menu_close);
        menuRecyclerView = view.findViewById(R.id.settings_menu_list);
    }

    private void setupMenuItems() {
        menuItems = new ArrayList<>();
        
        // 预算设置
        menuItems.add(new SettingsMenuItem(
                R.drawable.ic_budget,
                getString(R.string.receipt_预算设置),
                "管理您的预算和支出限制",
                SettingsMenuItem.SettingsMenuAction.BUDGET_SETTINGS
        ));
        
        // 分类管理
        menuItems.add(new SettingsMenuItem(
                R.drawable.ic_category,
                getString(R.string.receipt_分类管理),
                "管理收支分类和图标",
                SettingsMenuItem.SettingsMenuAction.CATEGORY_MANAGEMENT
        ));
        
        // 账户管理
        menuItems.add(new SettingsMenuItem(
                R.drawable.ic_account,
                getString(R.string.receipt_账户管理),
                "管理您的银行卡和现金账户",
                SettingsMenuItem.SettingsMenuAction.ACCOUNT_MANAGEMENT
        ));
        
        // 数据备份
        menuItems.add(new SettingsMenuItem(
                R.drawable.ic_backup,
                getString(R.string.receipt_数据备份),
                "备份和恢复您的记账数据",
                SettingsMenuItem.SettingsMenuAction.DATA_BACKUP
        ));
    }

    private void setupRecyclerView() {
        menuRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        menuAdapter = new SettingsMenuAdapter(getContext(), menuItems);
        menuAdapter.setOnItemClickListener(this);
        menuRecyclerView.setAdapter(menuAdapter);
    }

    private void setupClickListeners() {
        closeButton.setOnClickListener(v -> dismiss());
    }

    @Override
    public void onSettingsMenuItemClick(SettingsMenuItem item) {
        try {
            switch (item.getAction()) {
                case BUDGET_SETTINGS:
                    openBudgetSettings();
                    break;
                case CATEGORY_MANAGEMENT:
                    openCategoryManagement();
                    break;
                case ACCOUNT_MANAGEMENT:
                    openAccountManagement();
                    break;
                case DATA_BACKUP:
                    openDataBackup();
                    break;
                default:
                    Toast.makeText(getContext(), "功能开发中...", Toast.LENGTH_SHORT).show();
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling menu item click", e);
            Toast.makeText(getContext(), "打开功能失败", Toast.LENGTH_SHORT).show();
        }
    }

    private void openBudgetSettings() {
        try {
            Intent intent = new Intent(getActivity(), BudgetSettingsActivity.class);
            startActivity(intent);
            dismiss();
        } catch (Exception e) {
            Log.e(TAG, "Error opening budget settings", e);
            Toast.makeText(getContext(), "打开预算设置失败", Toast.LENGTH_SHORT).show();
        }
    }

    private void openCategoryManagement() {
        // TODO: 实现分类管理页面
        Toast.makeText(getContext(), "分类管理功能开发中...", Toast.LENGTH_SHORT).show();
        dismiss();
    }

    private void openAccountManagement() {
        // TODO: 实现账户管理页面
        Toast.makeText(getContext(), "账户管理功能开发中...", Toast.LENGTH_SHORT).show();
        dismiss();
    }

    private void openDataBackup() {
        // TODO: 实现数据备份页面
        Toast.makeText(getContext(), "数据备份功能开发中...", Toast.LENGTH_SHORT).show();
        dismiss();
    }

    private void setupFullHeight(BottomSheetDialog bottomSheetDialog) {
        try {
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                bottomSheet.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up full height", e);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 清理资源
        menuItems = null;
        menuAdapter = null;
    }
}
