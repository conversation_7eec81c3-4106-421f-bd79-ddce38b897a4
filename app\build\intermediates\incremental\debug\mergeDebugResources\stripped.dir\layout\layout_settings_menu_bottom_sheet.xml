<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_background"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/receipt_设置"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/settings_menu_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/receipt_关闭"
            android:src="@drawable/ic_close"
            app:tint="@color/black" />

    </LinearLayout>

    <!-- 设置菜单列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/settings_menu_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:nestedScrollingEnabled="false" />

</LinearLayout>
