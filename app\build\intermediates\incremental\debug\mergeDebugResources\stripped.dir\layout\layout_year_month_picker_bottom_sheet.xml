<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_background"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/receipt_选择年月"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/year_month_picker_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            android:text="@string/receipt_取消"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/year_month_picker_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            android:text="@string/receipt_确定"
            android:textColor="@color/HuaQing"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- 年月选择器容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="16dp"
        android:paddingBottom="16dp">

        <!-- 年份选择器 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/receipt_年份"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <NumberPicker
                android:id="@+id/year_picker"
                android:layout_width="wrap_content"
                android:layout_height="150dp"
                android:theme="@style/NumberPickerStyle" />

        </LinearLayout>

        <!-- 月份选择器 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/receipt_月份"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <NumberPicker
                android:id="@+id/month_picker"
                android:layout_width="wrap_content"
                android:layout_height="150dp"
                android:theme="@style/NumberPickerStyle" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
