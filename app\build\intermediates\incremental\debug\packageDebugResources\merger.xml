<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res"><file name="recording_page_error_shake" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\anim\recording_page_error_shake.xml" qualifiers="" type="anim"/><file name="main_activity_selector_selection_state" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\color\main_activity_selector_selection_state.xml" qualifiers="" type="color"/><file name="selector_tag_text" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\color\selector_tag_text.xml" qualifiers="" type="color"/><file name="switchmaterial_thumb_color" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\color\switchmaterial_thumb_color.xml" qualifiers="" type="color"/><file name="switchmaterial_track_color" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\color\switchmaterial_track_color.xml" qualifiers="" type="color"/><file name="account_icon_alipay" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\account_icon_alipay.xml" qualifiers="" type="drawable"/><file name="account_icon_cash" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\account_icon_cash.xml" qualifiers="" type="drawable"/><file name="account_icon_credit_card" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\account_icon_credit_card.xml" qualifiers="" type="drawable"/><file name="account_icon_debit_card" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\account_icon_debit_card.xml" qualifiers="" type="drawable"/><file name="account_icon_e_cny" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\account_icon_e_cny.xml" qualifiers="" type="drawable"/><file name="account_icon_huabei" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\account_icon_huabei.xml" qualifiers="" type="drawable"/><file name="account_icon_jd" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\account_icon_jd.xml" qualifiers="" type="drawable"/><file name="account_icon_wechat" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\account_icon_wechat.xml" qualifiers="" type="drawable"/><file name="budget_progress_bar" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\budget_progress_bar.xml" qualifiers="" type="drawable"/><file name="budget_status_indicator" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\budget_status_indicator.xml" qualifiers="" type="drawable"/><file name="button_outline_grey" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\button_outline_grey.xml" qualifiers="" type="drawable"/><file name="button_primary" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\button_primary.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="dialog_bottom_sheet_background" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\dialog_bottom_sheet_background.xml" qualifiers="" type="drawable"/><file name="frag_receipt_add_bg" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\frag_receipt_add_bg.xml" qualifiers="" type="drawable"/><file name="frag_receipt_add_image" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\frag_receipt_add_image.xml" qualifiers="" type="drawable"/><file name="frag_receipt_setting" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\frag_receipt_setting.xml" qualifiers="" type="drawable"/><file name="frag_recording_transfer_in_bg" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\frag_recording_transfer_in_bg.xml" qualifiers="" type="drawable"/><file name="frag_recording_transfer_out_bg" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\frag_recording_transfer_out_bg.xml" qualifiers="" type="drawable"/><file name="frag_recording_transfer_swap_bg" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\frag_recording_transfer_swap_bg.xml" qualifiers="" type="drawable"/><file name="frag_wallets_add" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\frag_wallets_add.xml" qualifiers="" type="drawable"/><file name="icon_add" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_add.xml" qualifiers="" type="drawable"/><file name="icon_arrow_back" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_arrow_back.xml" qualifiers="" type="drawable"/><file name="icon_arrow_down" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_arrow_down.xml" qualifiers="" type="drawable"/><file name="icon_arrow_right" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_arrow_right.xml" qualifiers="" type="drawable"/><file name="icon_bank_abc" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_abc.xml" qualifiers="" type="drawable"/><file name="icon_bank_bob" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_bob.xml" qualifiers="" type="drawable"/><file name="icon_bank_boc" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_boc.xml" qualifiers="" type="drawable"/><file name="icon_bank_bocom" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_bocom.xml" qualifiers="" type="drawable"/><file name="icon_bank_ccb" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_ccb.xml" qualifiers="" type="drawable"/><file name="icon_bank_cib" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_cib.xml" qualifiers="" type="drawable"/><file name="icon_bank_citic" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_citic.xml" qualifiers="" type="drawable"/><file name="icon_bank_cmb" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_cmb.xml" qualifiers="" type="drawable"/><file name="icon_bank_cmbc" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_cmbc.xml" qualifiers="" type="drawable"/><file name="icon_bank_gdb" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_gdb.xml" qualifiers="" type="drawable"/><file name="icon_bank_hxb" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_hxb.xml" qualifiers="" type="drawable"/><file name="icon_bank_icbc" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_icbc.xml" qualifiers="" type="drawable"/><file name="icon_bank_psbc" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_psbc.xml" qualifiers="" type="drawable"/><file name="icon_bank_spdb" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_bank_spdb.xml" qualifiers="" type="drawable"/><file name="icon_check" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_check.xml" qualifiers="" type="drawable"/><file name="icon_close" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_close.xml" qualifiers="" type="drawable"/><file name="icon_delete" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_delete.xml" qualifiers="" type="drawable"/><file name="icon_receipts" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_receipts.xml" qualifiers="" type="drawable"/><file name="icon_receipts_selected" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_receipts_selected.xml" qualifiers="" type="drawable"/><file name="icon_recording_airplane" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_airplane.xml" qualifiers="" type="drawable"/><file name="icon_recording_bike" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_bike.xml" qualifiers="" type="drawable"/><file name="icon_recording_bonus" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_bonus.xml" qualifiers="" type="drawable"/><file name="icon_recording_breakfast" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_breakfast.xml" qualifiers="" type="drawable"/><file name="icon_recording_bus" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_bus.xml" qualifiers="" type="drawable"/><file name="icon_recording_charge" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_charge.xml" qualifiers="" type="drawable"/><file name="icon_recording_clothes" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_clothes.xml" qualifiers="" type="drawable"/><file name="icon_recording_diet" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_diet.xml" qualifiers="" type="drawable"/><file name="icon_recording_dinner" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_dinner.xml" qualifiers="" type="drawable"/><file name="icon_recording_education" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_education.xml" qualifiers="" type="drawable"/><file name="icon_recording_entertainment" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_entertainment.xml" qualifiers="" type="drawable"/><file name="icon_recording_express_delivery" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_express_delivery.xml" qualifiers="" type="drawable"/><file name="icon_recording_financing" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_financing.xml" qualifiers="" type="drawable"/><file name="icon_recording_fruit" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_fruit.xml" qualifiers="" type="drawable"/><file name="icon_recording_fund" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_fund.xml" qualifiers="" type="drawable"/><file name="icon_recording_haircut" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_haircut.xml" qualifiers="" type="drawable"/><file name="icon_recording_housing_fund" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_housing_fund.xml" qualifiers="" type="drawable"/><file name="icon_recording_interest" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_interest.xml" qualifiers="" type="drawable"/><file name="icon_recording_investment" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_investment.xml" qualifiers="" type="drawable"/><file name="icon_recording_journey" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_journey.xml" qualifiers="" type="drawable"/><file name="icon_recording_juice" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_juice.xml" qualifiers="" type="drawable"/><file name="icon_recording_lunch" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_lunch.xml" qualifiers="" type="drawable"/><file name="icon_recording_makeup" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_makeup.xml" qualifiers="" type="drawable"/><file name="icon_recording_medicine" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_medicine.xml" qualifiers="" type="drawable"/><file name="icon_recording_midnight_snack" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_midnight_snack.xml" qualifiers="" type="drawable"/><file name="icon_recording_mobile_credit" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_mobile_credit.xml" qualifiers="" type="drawable"/><file name="icon_recording_others" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_others.xml" qualifiers="" type="drawable"/><file name="icon_recording_salary" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_salary.xml" qualifiers="" type="drawable"/><file name="icon_recording_snacks" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_snacks.xml" qualifiers="" type="drawable"/><file name="icon_recording_sports" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_sports.xml" qualifiers="" type="drawable"/><file name="icon_recording_stock" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_stock.xml" qualifiers="" type="drawable"/><file name="icon_recording_subway" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_subway.xml" qualifiers="" type="drawable"/><file name="icon_recording_taxi" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_taxi.xml" qualifiers="" type="drawable"/><file name="icon_recording_train" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_train.xml" qualifiers="" type="drawable"/><file name="icon_recording_transportation" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_transportation.xml" qualifiers="" type="drawable"/><file name="icon_recording_trip_allowance" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_trip_allowance.xml" qualifiers="" type="drawable"/><file name="icon_recording_vegetable" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_recording_vegetable.xml" qualifiers="" type="drawable"/><file name="icon_setting_in_recording" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_setting_in_recording.xml" qualifiers="" type="drawable"/><file name="icon_stats" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_stats.xml" qualifiers="" type="drawable"/><file name="icon_stats_selected" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_stats_selected.xml" qualifiers="" type="drawable"/><file name="icon_swap" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_swap.xml" qualifiers="" type="drawable"/><file name="icon_wallets" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_wallets.xml" qualifiers="" type="drawable"/><file name="icon_wallets_selected" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\icon_wallets_selected.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_category_food" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_category_food.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="recyclerview_item_selected_bg" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\recyclerview_item_selected_bg.xml" qualifiers="" type="drawable"/><file name="selector_tag_item" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\selector_tag_item.xml" qualifiers="" type="drawable"/><file name="style_edittext_bg" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\style_edittext_bg.xml" qualifiers="" type="drawable"/><file name="style_tag_color_picker_bg" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\style_tag_color_picker_bg.xml" qualifiers="" type="drawable"/><file name="style_tag_management_item_bg" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\style_tag_management_item_bg.xml" qualifiers="" type="drawable"/><file name="widget_common_bg" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\widget_common_bg.xml" qualifiers="" type="drawable"/><file name="widget_divider" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\widget_divider.xml" qualifiers="" type="drawable"/><file name="widget_icon_dot" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\widget_icon_dot.xml" qualifiers="" type="drawable"/><file name="widget_icon_dot_combine" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\widget_icon_dot_combine.xml" qualifiers="" type="drawable"/><file name="widget_icon_dot_disable" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\widget_icon_dot_disable.xml" qualifiers="" type="drawable"/><file name="widget_icon_dot_enable" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\widget_icon_dot_enable.xml" qualifiers="" type="drawable"/><file name="widget_small_button_bg" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\widget_small_button_bg.xml" qualifiers="" type="drawable"/><file name="widget_tag_item_bg" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\widget_tag_item_bg.xml" qualifiers="" type="drawable"/><file name="activity_budget_settings" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\activity_budget_settings.xml" qualifiers="" type="layout"/><file name="activity_fill_new_account_info" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\activity_fill_new_account_info.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_recording_page" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\activity_recording_page.xml" qualifiers="" type="layout"/><file name="activity_tag_management" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\activity_tag_management.xml" qualifiers="" type="layout"/><file name="dialog_add_new_account_bank_selection" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\dialog_add_new_account_bank_selection.xml" qualifiers="" type="layout"/><file name="dialog_add_new_account_list_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\dialog_add_new_account_list_view.xml" qualifiers="" type="layout"/><file name="dialog_budget_category_selection" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\dialog_budget_category_selection.xml" qualifiers="" type="layout"/><file name="dialog_credit_date_picker_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\dialog_credit_date_picker_view.xml" qualifiers="" type="layout"/><file name="dialog_date_time_picker_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\dialog_date_time_picker_view.xml" qualifiers="" type="layout"/><file name="dialog_include_in_stats_budget_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\dialog_include_in_stats_budget_view.xml" qualifiers="" type="layout"/><file name="dialog_recording_account_selection_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\dialog_recording_account_selection_view.xml" qualifiers="" type="layout"/><file name="dialog_recording_subcategory_list_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\dialog_recording_subcategory_list_view.xml" qualifiers="" type="layout"/><file name="dialog_recording_tag_selection_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\dialog_recording_tag_selection_view.xml" qualifiers="" type="layout"/><file name="dialog_recording_transfer_account_selection_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\dialog_recording_transfer_account_selection_view.xml" qualifiers="" type="layout"/><file name="dialog_tag_edit_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\dialog_tag_edit_view.xml" qualifiers="" type="layout"/><file name="fragment_receipt" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\fragment_receipt.xml" qualifiers="" type="layout"/><file name="fragment_stats" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\fragment_stats.xml" qualifiers="" type="layout"/><file name="fragment_wallet" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\fragment_wallet.xml" qualifiers="" type="layout"/><file name="item_account_category_in_wallets" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\item_account_category_in_wallets.xml" qualifiers="" type="layout"/><file name="item_account_type_category" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\item_account_type_category.xml" qualifiers="" type="layout"/><file name="item_budget_category" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\item_budget_category.xml" qualifiers="" type="layout"/><file name="item_budget_category_selection" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\item_budget_category_selection.xml" qualifiers="" type="layout"/><file name="item_recording_in_out_category_list_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\item_recording_in_out_category_list_view.xml" qualifiers="" type="layout"/><file name="item_recording_tag_category" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\item_recording_tag_category.xml" qualifiers="" type="layout"/><file name="item_recording_transfer_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\item_recording_transfer_view.xml" qualifiers="" type="layout"/><file name="item_tag_management_category" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\item_tag_management_category.xml" qualifiers="" type="layout"/><file name="style_account_bank_item_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_account_bank_item_view.xml" qualifiers="" type="layout"/><file name="style_account_item_in_wallets" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_account_item_in_wallets.xml" qualifiers="" type="layout"/><file name="style_account_type_item_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_account_type_item_view.xml" qualifiers="" type="layout"/><file name="style_daily_in_out_detail_tag_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_daily_in_out_detail_tag_view.xml" qualifiers="" type="layout"/><file name="style_daily_in_out_detail_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_daily_in_out_detail_view.xml" qualifiers="" type="layout"/><file name="style_daily_in_out_list_item_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_daily_in_out_list_item_view.xml" qualifiers="" type="layout"/><file name="style_navbar_item_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_navbar_item_view.xml" qualifiers="" type="layout"/><file name="style_recording_account_selection_item_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_recording_account_selection_item_view.xml" qualifiers="" type="layout"/><file name="style_recording_category_item_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_recording_category_item_view.xml" qualifiers="" type="layout"/><file name="style_recording_tag_selectable" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_recording_tag_selectable.xml" qualifiers="" type="layout"/><file name="style_recording_transfer_account_selection_item_view" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_recording_transfer_account_selection_item_view.xml" qualifiers="" type="layout"/><file name="style_tag_color_picker" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_tag_color_picker.xml" qualifiers="" type="layout"/><file name="style_tag_management_item" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\style_tag_management_item.xml" qualifiers="" type="layout"/><file name="widget_budget" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\widget_budget.xml" qualifiers="" type="layout"/><file name="widget_in_and_out" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\widget_in_and_out.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="grey">#808080</color><color name="defaultGrey">#8A000000</color><color name="light_grey">#d3d3d3</color><color name="HuaQing">#2376b7</color><color name="YinBai">#f1f0ed</color><color name="ChaHuaHong">#ee3f4d</color><color name="WaLv">#45b787</color><color name="QianHui">#dad4cb</color><color name="delete_red">#F44336</color></file><file path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="divider_height">1dp</dimen><dimen name="divider_left_margin">25dp</dimen><dimen name="divider_right_margin">25dp</dimen><dimen name="divider_in_wallets_left_margin">15dp</dimen><dimen name="divider_in_wallets_right_margin">15dp</dimen><dimen name="recording_transfer_account_icon_size">40dp</dimen><dimen name="recording_transfer_account_icon_padding">1dp</dimen><dimen name="recording_transfer_account_icon_circle_radius">20dp</dimen><dimen name="recording_transfer_account_name_padding_start">18dp</dimen></file><file path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">LikeQianWang</string><string name="title_账单">账单</string><string name="title_钱包">钱包</string><string name="title_统计">统计</string><string name="receipt_账本">默认账本</string><string name="in_out_总支出">总支出</string><string name="in_out_总收入">总收入</string><string name="in_out_结余">结余</string><string name="budget_剩余预算">剩余预算</string><string name="budget_总预算">总预算</string><string name="wallet_净资产">净资产</string><string name="wallet_总资产">总资产</string><string name="wallet_总负债">总负债</string><string name="wallet_账户不计入资产">不计入</string><string name="add_new_account_选择类型">选择类型</string><string name="add_new_account_选择银行">选择银行</string><string name="add_new_account_返回">返回</string><string name="add_new_account_添加账户">添加账户</string><string name="add_new_account_保存">保存</string><string name="add_new_account_账户名称">账户名称</string><string name="add_new_account_账户备注">账户备注</string><string name="add_new_account_账户余额">账户余额</string><string name="add_new_account_账户币种">账户币种</string><string name="add_new_account_计入总资产">计入总资产</string><string name="add_new_account_总额度">总额度</string><string name="add_new_account_当前欠款">当前欠款</string><string name="add_new_account_账单日">账单日</string><string name="add_new_account_还款日">还款日</string><string name="add_new_account_出账日账单计入当期">出账日账单计入当期</string><string name="add_new_account_每月">每月</string><string name="add_new_account_日">日</string><string name="add_new_account_hint_请输入账户名称">请输入账户名称</string><string name="add_new_account_hint_点击输入备注">点击输入备注 (可不填)</string><string name="add_new_account_hint_0.00">0.00</string><string name="add_new_account_hint_请点击设置">请点击设置</string><string name="add_new_account_hint_搜索">搜索</string><string name="recording_输入收支备注">点此输入备注…</string><string name="recording_select_account">选择账户</string><string name="recording_tags">标签</string><string name="recording_labels">标记</string><string name="确定">确定</string><string name="取消">取消</string><string name="不计入收支">不计入收支</string><string name="不计入预算">不计入预算</string><string name="标签管理">标签管理</string><string name="recording_transfer_select_transfer_out_account">请输入转出账户</string><string name="recording_transfer_select_transfer_in_account">请输入转入账户</string><string name="inputKeyboard_1">1</string><string name="inputKeyboard_2">2</string><string name="inputKeyboard_3">3</string><string name="inputKeyboard_4">4</string><string name="inputKeyboard_5">5</string><string name="inputKeyboard_6">6</string><string name="inputKeyboard_7">7</string><string name="inputKeyboard_8">8</string><string name="inputKeyboard_9">9</string><string name="inputKeyboard_0">0</string><string name="inputKeyboard_plus">+</string><string name="inputKeyboard_minus">-</string><string name="inputKeyboard_dot">.</string><string name="inputKeyboard_AddAnother">再记</string><string name="inputKeyboard_confirm">确定</string><string name="receipt_desc_新增">新增一笔记录</string><string name="receipt_desc_设置">用户设置</string><string name="receipt_desc_主界面导航栏">主界面导航栏</string><string name="receipt_desc_收支预算组件导航栏">收支预算组件圆点导航</string><string name="receipt_desc_收支示意圆点">收支示意圆点</string><string name="receipt_desc_标签图标">标签图标</string><string name="recording_page_desc_关闭页面">关闭记录页面</string><string name="recording_page_desc_收支转账导航栏">收支转账导航栏</string><string name="recording_page_desc_类型图标">类型图标</string><string name="recording_dialog_desc_收支子类型列表">收支子类型列表</string><string name="recording_transfer_dialog_desc_账户列表">账户列表</string><string name="wallets_desc_账户图标">账户图标</string><string name="wallets_desc_返回选择类型">返回选择类型</string><string name="wallets_desc_账户类型列表">账户类型列表</string><string name="wallets_desc_银行列表">银行列表</string><string name="budget_预算设置">预算设置</string><string name="budget_保存">保存</string><string name="budget_预算周期">预算周期</string><string name="budget_月度预算">月度</string><string name="budget_周度预算">周度</string><string name="budget_年度预算">年度</string><string name="budget_总预算设置">总预算设置</string><string name="budget_请输入总预算金额">请输入总预算金额</string><string name="budget_预警阈值">预警阈值</string><string name="budget_分类预算设置">分类预算设置</string><string name="budget_添加分类预算">添加分类</string><string name="budget_预算提醒设置">预算提醒设置</string><string name="budget_启用预算提醒">启用预算提醒</string><string name="budget_超预算提醒">超预算提醒</string><string name="budget_请输入预算金额">请输入预算金额</string><string name="budget_预警">预警</string><string name="budget_选择分类">选择分类</string><string name="budget_取消">取消</string><string name="budget_确定">确定</string><string name="budget_已用预算">已用预算</string><string name="budget_预算进度">预算进度</string><string name="budget_超出预算">超出预算</string><string name="budget_预算充足">预算充足</string><string name="budget_预算紧张">预算紧张</string><string name="budget_desc_返回">返回</string><string name="budget_desc_分类图标">分类图标</string><string name="budget_desc_删除分类预算">删除分类预算</string><string name="receipt_确定">确定</string><string name="receipt_退款原因">退款原因</string><string name="receipt_设置">设置</string><string name="receipt_分类管理">分类管理</string><string name="receipt_退款金额">退款金额</string><string name="receipt_desc_时间图标">时间图标</string><string name="receipt_原交易信息">原交易信息</string><string name="receipt_预算设置">预算设置</string><string name="receipt_desc_账户图标">账户图标</string><string name="receipt_desc_备注图标">备注图标</string><string name="receipt_交易详情">交易详情</string><string name="receipt_desc_交易类型图标">交易类型图标</string><string name="receipt_请输入退款金额">请输入退款金额</string><string name="receipt_请输入退款原因">请输入退款原因</string><string name="receipt_月份">月份</string><string name="receipt_desc_年月选择">年月选择</string><string name="receipt_取消">取消</string><string name="receipt_确认删除">确认删除</string><string name="receipt_选择年月">选择年月</string><string name="receipt_年份">年份</string><string name="receipt_删除">删除</string><string name="receipt_全额退款">全额退款</string><string name="receipt_desc_箭头">箭头</string><string name="receipt_账户管理">账户管理</string><string name="receipt_desc_分类图标">分类图标</string><string name="receipt_desc_设置图标">设置图标</string><string name="receipt_编辑">编辑</string><string name="receipt_关闭">关闭</string><string name="receipt_数据备份">数据备份</string><string name="receipt_退款">退款</string><string name="receipt_删除交易确认">确定要删除这笔交易吗？</string></file><file path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\values\styles.xml" qualifiers=""><style name="隐藏小白条">
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style><style name="软键盘按键样式">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">54dp</item>
        <item name="android:layout_columnWeight">1</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:background">@drawable/widget_small_button_bg</item>
        <item name="backgroundTint">@color/white</item>
        <item name="android:stateListAnimator">@null</item>
    </style><style name="记账选项按键样式">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:background">@drawable/widget_small_button_bg</item>
        <item name="backgroundTint">@color/white</item>
        <item name="android:stateListAnimator">@null</item>
    </style><style name="CustomBottomSheetDialog" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheet</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustNothing</item>
    </style><style name="CustomBottomSheet" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">@android:color/white</item>
        <item name="android:elevation">16dp</item>
    </style><style name="CustomShapeAppearanceBottomSheetDialog" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">20dp</item>
        <item name="cornerSizeTopLeft">20dp</item>
    </style><style name="DatePickerStyle" parent="android:Widget.Material.DatePicker">
        <item name="android:textColorPrimary">#333333</item>
        <item name="android:textSize">16sp</item>
        <item name="colorControlNormal">@color/HuaQing</item>
        <item name="colorAccent">@color/HuaQing</item>
    </style><style name="TimePickerStyle" parent="android:Widget.Material.TimePicker">
        <item name="android:textColorPrimary">#333333</item>
        <item name="android:textSize">16sp</item>
        <item name="colorControlNormal">@color/HuaQing</item>
        <item name="colorAccent">@color/HuaQing</item>
    </style><style name="circleIconStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style><style name="NumberPickerStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:colorControlNormal">@color/HuaQing</item>
    </style></file><file path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.LikeQianWang" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/HuaQing</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">#00000000</item>
        
        <item name="android:navigationBarColor">#00000000</item>
    </style></file><file path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.LikeQianWang" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="budget_remaining_progress_bar" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\budget_remaining_progress_bar.xml" qualifiers="" type="drawable"/><file name="budget_progress_bar_simple" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\budget_progress_bar_simple.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="ic_account" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_account.xml" qualifiers="" type="drawable"/><file name="ic_arrow_forward" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_arrow_forward.xml" qualifiers="" type="drawable"/><file name="ic_backup" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_backup.xml" qualifiers="" type="drawable"/><file name="ic_budget" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_budget.xml" qualifiers="" type="drawable"/><file name="ic_category" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_category.xml" qualifiers="" type="drawable"/><file name="ic_close" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_dropdown_arrow" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_dropdown_arrow.xml" qualifiers="" type="drawable"/><file name="ic_expense" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_expense.xml" qualifiers="" type="drawable"/><file name="ic_income" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_income.xml" qualifiers="" type="drawable"/><file name="ic_note" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_note.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_tag" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_tag.xml" qualifiers="" type="drawable"/><file name="ic_time" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_time.xml" qualifiers="" type="drawable"/><file name="ic_transfer" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\drawable\ic_transfer.xml" qualifiers="" type="drawable"/><file name="item_settings_menu" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\item_settings_menu.xml" qualifiers="" type="layout"/><file name="layout_refund_bottom_sheet" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\layout_refund_bottom_sheet.xml" qualifiers="" type="layout"/><file name="layout_settings_menu_bottom_sheet" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\layout_settings_menu_bottom_sheet.xml" qualifiers="" type="layout"/><file name="layout_transaction_detail_bottom_sheet" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\layout_transaction_detail_bottom_sheet.xml" qualifiers="" type="layout"/><file name="layout_year_month_picker_bottom_sheet" path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\res\layout\layout_year_month_picker_bottom_sheet.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\LikeQianWang\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\LikeQianWang\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\LikeQianWang\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>