package com.example.likeqianwang.ui.recurring_transactions;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.RecurringTransaction;
import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.RecurringTransactionViewModel;
import com.example.likeqianwang.adapters.RecurringTransactionAdapter;
import com.example.likeqianwang.ui.dialogs.RecurringTransactionBottomSheetDialogFragment;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.ArrayList;
import java.util.List;

public class RecurringTransactionsActivity extends AppCompatActivity 
        implements RecurringTransactionAdapter.OnRecurringTransactionItemClickListener {

    private static final String TAG = "RecurringTransactions";

    // UI组件
    private ImageView backButton;
    private TextView titleText;
    private RecyclerView recyclerView;
    private FloatingActionButton fabAdd;
    private View emptyView;
    private TextView emptyText;

    // ViewModel和适配器
    private RecurringTransactionViewModel viewModel;
    private RecurringTransactionAdapter adapter;
    private List<RecurringTransaction> recurringTransactions;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_recurring_transactions);

        initViews();
        initViewModel();
        setupRecyclerView();
        setupClickListeners();
        observeData();
    }

    private void initViews() {
        backButton = findViewById(R.id.recurring_transactions_back);
        titleText = findViewById(R.id.recurring_transactions_title);
        recyclerView = findViewById(R.id.recurring_transactions_recycler_view);
        fabAdd = findViewById(R.id.recurring_transactions_fab_add);
        emptyView = findViewById(R.id.recurring_transactions_empty_view);
        emptyText = findViewById(R.id.recurring_transactions_empty_text);

        titleText.setText(R.string.receipt_周期记账);
        emptyText.setText("暂无周期记账记录\n点击右下角按钮添加");
    }

    private void initViewModel() {
        viewModel = new ViewModelProvider(this).get(RecurringTransactionViewModel.class);
    }

    private void setupRecyclerView() {
        recurringTransactions = new ArrayList<>();
        adapter = new RecurringTransactionAdapter(this, recurringTransactions);
        adapter.setOnItemClickListener(this);
        
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }

    private void setupClickListeners() {
        backButton.setOnClickListener(v -> finish());
        
        fabAdd.setOnClickListener(v -> showAddRecurringTransactionDialog());
    }

    private void observeData() {
        viewModel.getAllRecurringTransactions().observe(this, transactions -> {
            if (transactions != null) {
                Log.d(TAG, "Received " + transactions.size() + " recurring transactions");
                
                recurringTransactions.clear();
                recurringTransactions.addAll(transactions);
                adapter.notifyDataSetChanged();
                
                // 显示/隐藏空状态
                if (transactions.isEmpty()) {
                    emptyView.setVisibility(View.VISIBLE);
                    recyclerView.setVisibility(View.GONE);
                } else {
                    emptyView.setVisibility(View.GONE);
                    recyclerView.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    private void showAddRecurringTransactionDialog() {
        try {
            RecurringTransactionBottomSheetDialogFragment dialog = 
                    RecurringTransactionBottomSheetDialogFragment.newInstance(null);
            dialog.show(getSupportFragmentManager(), "AddRecurringTransaction");
        } catch (Exception e) {
            Log.e(TAG, "Error showing add recurring transaction dialog", e);
            Toast.makeText(this, "打开新增对话框失败", Toast.LENGTH_SHORT).show();
        }
    }

    private void showEditRecurringTransactionDialog(RecurringTransaction recurringTransaction) {
        try {
            RecurringTransactionBottomSheetDialogFragment dialog = 
                    RecurringTransactionBottomSheetDialogFragment.newInstance(recurringTransaction);
            dialog.show(getSupportFragmentManager(), "EditRecurringTransaction");
        } catch (Exception e) {
            Log.e(TAG, "Error showing edit recurring transaction dialog", e);
            Toast.makeText(this, "打开编辑对话框失败", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onRecurringTransactionItemClick(RecurringTransaction recurringTransaction) {
        showEditRecurringTransactionDialog(recurringTransaction);
    }

    @Override
    public void onRecurringTransactionToggleActive(RecurringTransaction recurringTransaction) {
        try {
            boolean newActiveStatus = !recurringTransaction.isActive();
            Log.d(TAG, "Toggling active status for " + recurringTransaction.getName() + " to " + newActiveStatus);
            
            viewModel.updateActiveStatus(recurringTransaction.getRecurringId(), newActiveStatus, 
                    new RecurringTransactionViewModel.RecurringTransactionOperationCallback() {
                        @Override
                        public void onSuccess() {
                            runOnUiThread(() -> {
                                String message = newActiveStatus ? "已启用周期记账" : "已禁用周期记账";
                                Toast.makeText(RecurringTransactionsActivity.this, message, Toast.LENGTH_SHORT).show();
                            });
                        }

                        @Override
                        public void onError(String error) {
                            runOnUiThread(() -> {
                                Toast.makeText(RecurringTransactionsActivity.this, 
                                        "操作失败: " + error, Toast.LENGTH_SHORT).show();
                            });
                        }
                    });
        } catch (Exception e) {
            Log.e(TAG, "Error toggling active status", e);
            Toast.makeText(this, "操作失败", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onRecurringTransactionDelete(RecurringTransaction recurringTransaction) {
        try {
            Log.d(TAG, "Deleting recurring transaction: " + recurringTransaction.getName());
            
            viewModel.deleteRecurringTransaction(recurringTransaction, 
                    new RecurringTransactionViewModel.RecurringTransactionOperationCallback() {
                        @Override
                        public void onSuccess() {
                            runOnUiThread(() -> {
                                Toast.makeText(RecurringTransactionsActivity.this, 
                                        "周期记账已删除", Toast.LENGTH_SHORT).show();
                            });
                        }

                        @Override
                        public void onError(String error) {
                            runOnUiThread(() -> {
                                Toast.makeText(RecurringTransactionsActivity.this, 
                                        "删除失败: " + error, Toast.LENGTH_SHORT).show();
                            });
                        }
                    });
        } catch (Exception e) {
            Log.e(TAG, "Error deleting recurring transaction", e);
            Toast.makeText(this, "删除失败", Toast.LENGTH_SHORT).show();
        }
    }
}
