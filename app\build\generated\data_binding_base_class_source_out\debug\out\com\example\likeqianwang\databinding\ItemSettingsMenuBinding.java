// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSettingsMenuBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView settingsMenuItemArrow;

  @NonNull
  public final ImageView settingsMenuItemIcon;

  @NonNull
  public final TextView settingsMenuItemSubtitle;

  @NonNull
  public final TextView settingsMenuItemTitle;

  private ItemSettingsMenuBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView settingsMenuItemArrow, @NonNull ImageView settingsMenuItemIcon,
      @NonNull TextView settingsMenuItemSubtitle, @NonNull TextView settingsMenuItemTitle) {
    this.rootView = rootView;
    this.settingsMenuItemArrow = settingsMenuItemArrow;
    this.settingsMenuItemIcon = settingsMenuItemIcon;
    this.settingsMenuItemSubtitle = settingsMenuItemSubtitle;
    this.settingsMenuItemTitle = settingsMenuItemTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSettingsMenuBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSettingsMenuBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_settings_menu, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSettingsMenuBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.settings_menu_item_arrow;
      ImageView settingsMenuItemArrow = ViewBindings.findChildViewById(rootView, id);
      if (settingsMenuItemArrow == null) {
        break missingId;
      }

      id = R.id.settings_menu_item_icon;
      ImageView settingsMenuItemIcon = ViewBindings.findChildViewById(rootView, id);
      if (settingsMenuItemIcon == null) {
        break missingId;
      }

      id = R.id.settings_menu_item_subtitle;
      TextView settingsMenuItemSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (settingsMenuItemSubtitle == null) {
        break missingId;
      }

      id = R.id.settings_menu_item_title;
      TextView settingsMenuItemTitle = ViewBindings.findChildViewById(rootView, id);
      if (settingsMenuItemTitle == null) {
        break missingId;
      }

      return new ItemSettingsMenuBinding((LinearLayout) rootView, settingsMenuItemArrow,
          settingsMenuItemIcon, settingsMenuItemSubtitle, settingsMenuItemTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
