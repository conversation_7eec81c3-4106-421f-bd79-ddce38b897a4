// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutSettingsMenuBottomSheetBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView settingsMenuClose;

  @NonNull
  public final RecyclerView settingsMenuList;

  private LayoutSettingsMenuBottomSheetBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView settingsMenuClose, @NonNull RecyclerView settingsMenuList) {
    this.rootView = rootView;
    this.settingsMenuClose = settingsMenuClose;
    this.settingsMenuList = settingsMenuList;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutSettingsMenuBottomSheetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutSettingsMenuBottomSheetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_settings_menu_bottom_sheet, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutSettingsMenuBottomSheetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.settings_menu_close;
      ImageView settingsMenuClose = ViewBindings.findChildViewById(rootView, id);
      if (settingsMenuClose == null) {
        break missingId;
      }

      id = R.id.settings_menu_list;
      RecyclerView settingsMenuList = ViewBindings.findChildViewById(rootView, id);
      if (settingsMenuList == null) {
        break missingId;
      }

      return new LayoutSettingsMenuBottomSheetBinding((LinearLayout) rootView, settingsMenuClose,
          settingsMenuList);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
