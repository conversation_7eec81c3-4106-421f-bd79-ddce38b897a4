// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutRefundBottomSheetBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final EditText refundAmountInput;

  @NonNull
  public final TextView refundCancel;

  @NonNull
  public final TextView refundConfirm;

  @NonNull
  public final TextView refundFullAmountButton;

  @NonNull
  public final TextView refundOriginalAmount;

  @NonNull
  public final TextView refundOriginalCategory;

  @NonNull
  public final ImageView refundOriginalIcon;

  @NonNull
  public final EditText refundReasonInput;

  private LayoutRefundBottomSheetBinding(@NonNull LinearLayout rootView,
      @NonNull EditText refundAmountInput, @NonNull TextView refundCancel,
      @NonNull TextView refundConfirm, @NonNull TextView refundFullAmountButton,
      @NonNull TextView refundOriginalAmount, @NonNull TextView refundOriginalCategory,
      @NonNull ImageView refundOriginalIcon, @NonNull EditText refundReasonInput) {
    this.rootView = rootView;
    this.refundAmountInput = refundAmountInput;
    this.refundCancel = refundCancel;
    this.refundConfirm = refundConfirm;
    this.refundFullAmountButton = refundFullAmountButton;
    this.refundOriginalAmount = refundOriginalAmount;
    this.refundOriginalCategory = refundOriginalCategory;
    this.refundOriginalIcon = refundOriginalIcon;
    this.refundReasonInput = refundReasonInput;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutRefundBottomSheetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutRefundBottomSheetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_refund_bottom_sheet, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutRefundBottomSheetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.refund_amount_input;
      EditText refundAmountInput = ViewBindings.findChildViewById(rootView, id);
      if (refundAmountInput == null) {
        break missingId;
      }

      id = R.id.refund_cancel;
      TextView refundCancel = ViewBindings.findChildViewById(rootView, id);
      if (refundCancel == null) {
        break missingId;
      }

      id = R.id.refund_confirm;
      TextView refundConfirm = ViewBindings.findChildViewById(rootView, id);
      if (refundConfirm == null) {
        break missingId;
      }

      id = R.id.refund_full_amount_button;
      TextView refundFullAmountButton = ViewBindings.findChildViewById(rootView, id);
      if (refundFullAmountButton == null) {
        break missingId;
      }

      id = R.id.refund_original_amount;
      TextView refundOriginalAmount = ViewBindings.findChildViewById(rootView, id);
      if (refundOriginalAmount == null) {
        break missingId;
      }

      id = R.id.refund_original_category;
      TextView refundOriginalCategory = ViewBindings.findChildViewById(rootView, id);
      if (refundOriginalCategory == null) {
        break missingId;
      }

      id = R.id.refund_original_icon;
      ImageView refundOriginalIcon = ViewBindings.findChildViewById(rootView, id);
      if (refundOriginalIcon == null) {
        break missingId;
      }

      id = R.id.refund_reason_input;
      EditText refundReasonInput = ViewBindings.findChildViewById(rootView, id);
      if (refundReasonInput == null) {
        break missingId;
      }

      return new LayoutRefundBottomSheetBinding((LinearLayout) rootView, refundAmountInput,
          refundCancel, refundConfirm, refundFullAmountButton, refundOriginalAmount,
          refundOriginalCategory, refundOriginalIcon, refundReasonInput);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
