// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.NumberPicker;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutYearMonthPickerBottomSheetBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final NumberPicker monthPicker;

  @NonNull
  public final TextView yearMonthPickerCancel;

  @NonNull
  public final TextView yearMonthPickerConfirm;

  @NonNull
  public final NumberPicker yearPicker;

  private LayoutYearMonthPickerBottomSheetBinding(@NonNull LinearLayout rootView,
      @NonNull NumberPicker monthPicker, @NonNull TextView yearMonthPickerCancel,
      @NonNull TextView yearMonthPickerConfirm, @NonNull NumberPicker yearPicker) {
    this.rootView = rootView;
    this.monthPicker = monthPicker;
    this.yearMonthPickerCancel = yearMonthPickerCancel;
    this.yearMonthPickerConfirm = yearMonthPickerConfirm;
    this.yearPicker = yearPicker;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutYearMonthPickerBottomSheetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutYearMonthPickerBottomSheetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_year_month_picker_bottom_sheet, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutYearMonthPickerBottomSheetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.month_picker;
      NumberPicker monthPicker = ViewBindings.findChildViewById(rootView, id);
      if (monthPicker == null) {
        break missingId;
      }

      id = R.id.year_month_picker_cancel;
      TextView yearMonthPickerCancel = ViewBindings.findChildViewById(rootView, id);
      if (yearMonthPickerCancel == null) {
        break missingId;
      }

      id = R.id.year_month_picker_confirm;
      TextView yearMonthPickerConfirm = ViewBindings.findChildViewById(rootView, id);
      if (yearMonthPickerConfirm == null) {
        break missingId;
      }

      id = R.id.year_picker;
      NumberPicker yearPicker = ViewBindings.findChildViewById(rootView, id);
      if (yearPicker == null) {
        break missingId;
      }

      return new LayoutYearMonthPickerBottomSheetBinding((LinearLayout) rootView, monthPicker,
          yearMonthPickerCancel, yearMonthPickerConfirm, yearPicker);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
