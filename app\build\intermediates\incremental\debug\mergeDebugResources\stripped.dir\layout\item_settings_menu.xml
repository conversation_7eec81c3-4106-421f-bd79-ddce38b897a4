<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="16dp">

    <ImageView
        android:id="@+id/settings_menu_item_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="16dp"
        android:contentDescription="@string/receipt_desc_设置图标"
        app:tint="@color/black"
        tools:src="@drawable/ic_settings" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/settings_menu_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="16sp"
            tools:text="预算设置" />

        <TextView
            android:id="@+id/settings_menu_item_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/black"
            android:textSize="12sp"
            android:visibility="gone"
            tools:text="管理您的预算和支出限制"
            tools:visibility="visible" />

    </LinearLayout>

    <ImageView
        android:id="@+id/settings_menu_item_arrow"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:contentDescription="@string/receipt_desc_箭头"
        android:src="@drawable/ic_arrow_forward"
        app:tint="@color/black" />

</LinearLayout>
