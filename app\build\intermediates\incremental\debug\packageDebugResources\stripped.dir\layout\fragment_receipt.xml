<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.main_receipts.ReceiptsFragment">

    <TextView
        android:id="@+id/receipt_Receipts"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="15dp"
        android:text="@string/receipt_账本"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/receipt_Setting"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:adjustViewBounds="true"
        android:contentDescription="@string/receipt_desc_设置"
        android:padding="15dp"
        android:scaleType="fitXY"
        android:src="@drawable/frag_receipt_setting"
        app:layout_constraintBottom_toBottomOf="@id/receipt_Receipts"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/HuaQing" />

    <LinearLayout
        android:id="@+id/receipt_Year_Month_Container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="15dp"
        android:paddingTop="8dp"
        android:paddingEnd="15dp"
        android:paddingBottom="8dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/receipt_Receipts">

        <TextView
            android:id="@+id/receipt_Year_Month"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2025年2月"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/receipt_Year_Month_Arrow"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="4dp"
            android:contentDescription="@string/receipt_desc_年月选择"
            android:src="@drawable/ic_dropdown_arrow"
            app:tint="@color/black" />

    </LinearLayout>

    <com.example.likeqianwang.Utils.NestedScrollableHost
        android:id="@+id/receipt_InOut_Budget_Container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/receipt_Year_Month_Container">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/receipt_InOut_Budget_Widget"
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:layout_margin="15dp"
            android:background="@drawable/widget_common_bg"
            android:nestedScrollingEnabled="true" />

    </com.example.likeqianwang.Utils.NestedScrollableHost>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/receipt_InOut_Budget_WidgetTabs"
        android:layout_width="22dp"
        android:layout_height="10dp"
        android:layout_marginBottom="20dp"
        android:contentDescription="@string/receipt_desc_收支预算组件导航栏"
        app:layout_constraintBottom_toBottomOf="@id/receipt_InOut_Budget_Container"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:tabBackground="@null"
        app:tabGravity="fill"
        app:tabIndicatorHeight="0dp"
        app:tabMode="fixed"
        app:tabRippleColor="@null" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/receipt_Daily_InOut_List"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/receipt_InOut_Budget_Container"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageButton
        android:id="@+id/button_Add"
        style="?android:borderlessButtonStyle"
        android:layout_width="65dp"
        android:layout_height="65dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/frag_receipt_add_bg"
        android:contentDescription="@string/receipt_desc_新增"
        android:elevation="4dp"
        android:padding="12dp"
        android:scaleType="fitCenter"
        android:src="@drawable/frag_receipt_add_image"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:tint="@color/white" />

</androidx.constraintlayout.widget.ConstraintLayout>