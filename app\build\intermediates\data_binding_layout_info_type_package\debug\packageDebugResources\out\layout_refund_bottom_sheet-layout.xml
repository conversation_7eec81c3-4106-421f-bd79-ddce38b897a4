<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_refund_bottom_sheet" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\layout_refund_bottom_sheet.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/layout_refund_bottom_sheet_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="211" endOffset="14"/></Target><Target id="@+id/refund_cancel" view="TextView"><Expressions/><location startLine="26" startOffset="8" endLine="34" endOffset="37"/></Target><Target id="@+id/refund_confirm" view="TextView"><Expressions/><location startLine="36" startOffset="8" endLine="46" endOffset="38"/></Target><Target id="@+id/refund_original_icon" view="ImageView"><Expressions/><location startLine="79" startOffset="16" endLine="84" endOffset="78"/></Target><Target id="@+id/refund_original_category" view="TextView"><Expressions/><location startLine="86" startOffset="16" endLine="92" endOffset="45"/></Target><Target id="@+id/refund_original_amount" view="TextView"><Expressions/><location startLine="94" startOffset="16" endLine="100" endOffset="46"/></Target><Target id="@+id/refund_amount_input" view="EditText"><Expressions/><location startLine="145" startOffset="16" endLine="155" endOffset="45"/></Target><Target id="@+id/refund_full_amount_button" view="TextView"><Expressions/><location startLine="157" startOffset="16" endLine="165" endOffset="45"/></Target><Target id="@+id/refund_reason_input" view="EditText"><Expressions/><location startLine="195" startOffset="12" endLine="205" endOffset="41"/></Target></Targets></Layout>